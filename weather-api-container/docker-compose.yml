version: '3.8'

services:
  weather-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      # Weather Station Configuration
      - STATION_ID=185807
      - TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c
      - AIRPORT_ADVISORY=Ridge Landing Airpark automated advisory
      
      # TTS Configuration
      - TTS_VOICE=en-US-JennyNeural
      - WORD_GAP=-0.8
      - PHRASE_GAP=0.2
      
      # Service Configuration
      - UPDATE_INTERVAL_SECONDS=120
      - HOST=0.0.0.0
      - PORT=8000
      - BASE_URL=http://localhost:8000
      
    volumes:
      - audio_data:/app/audio_components
      - output_data:/app/output
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  audio_data:
  output_data:
